{"version": "2.0.0", "tasks": [{"type": "shell", "command": "npm run dev", "label": "start-dev-server", "group": {"kind": "build", "isDefault": true}, "isBackground": true, "problemMatcher": {"owner": "typescript", "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "VITE v\\d+\\.\\d+\\.\\d+\\s+ready in\\s+\\d+\\s+ms"}}}, "presentation": {"reveal": "always", "panel": "dedicated", "clear": true}, "detail": "Start Vite Dev Server"}, {"type": "npm", "script": "build", "group": "build", "problemMatcher": ["$tsc"], "detail": "Build Production Code"}, {"type": "npm", "script": "preview", "problemMatcher": [], "detail": "Preview Production Build"}, {"type": "npm", "script": "lint", "problemMatcher": ["$eslint-stylish"], "detail": "Run ESLint Code Check"}]}