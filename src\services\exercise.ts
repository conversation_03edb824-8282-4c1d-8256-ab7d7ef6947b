import { put } from './api';

// 老师信息接口
export interface TeacherInfo {
  tid: number;
  tname: string;
  tavatar?: string | null;
  tavatarCache?: string | null; // base64缓存的头像数据
  cacheTime?: number; // 缓存时间戳
}

// 本地存储老师信息的key
const TEACHER_STORAGE_KEY = 'teacher_info_cache';

// 头像缓存过期时间（7天）
const AVATAR_CACHE_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * 下载图片并转换为base64
 * @param imageUrl 图片URL
 * @returns Promise<string | null> base64字符串或null
 */
const downloadImageAsBase64 = async (imageUrl: string): Promise<string | null> => {
  try {
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const blob = await response.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('下载图片失败:', error);
    return null;
  }
};

/**
 * 检查头像缓存是否过期
 * @param cacheTime 缓存时间戳
 * @returns boolean 是否过期
 */
const isAvatarCacheExpired = (cacheTime?: number): boolean => {
  if (!cacheTime) return true;
  return Date.now() - cacheTime > AVATAR_CACHE_EXPIRE_TIME;
};

/**
 * 保存老师信息到本地存储（包含头像缓存）
 * @param teacherInfo 老师信息
 */
export const saveTeacherInfo = async (teacherInfo: TeacherInfo): Promise<void> => {
  try {
    const existingData = localStorage.getItem(TEACHER_STORAGE_KEY);
    const teacherCache: Record<number, TeacherInfo> = existingData ? JSON.parse(existingData) : {};
    
    // 获取现有的老师信息
    const existingTeacher = teacherCache[teacherInfo.tid];
    
    // 如果有新的头像URL且与现有缓存不同，或者缓存已过期，则下载新头像
    if (teacherInfo.tavatar && 
        (teacherInfo.tavatar !== existingTeacher?.tavatar || 
         isAvatarCacheExpired(existingTeacher?.cacheTime))) {
      
      const avatarBase64 = await downloadImageAsBase64(teacherInfo.tavatar);
      
      if (avatarBase64) {
        teacherInfo.tavatarCache = avatarBase64;
        teacherInfo.cacheTime = Date.now();
      } else {
        // 如果下载失败，保留现有缓存
        if (existingTeacher?.tavatarCache && !isAvatarCacheExpired(existingTeacher.cacheTime)) {
          teacherInfo.tavatarCache = existingTeacher.tavatarCache;
          teacherInfo.cacheTime = existingTeacher.cacheTime;
        }
        console.warn('老师头像缓存失败，使用现有缓存或无头像');
      }
    } else if (existingTeacher?.tavatarCache && !isAvatarCacheExpired(existingTeacher.cacheTime)) {
      // 如果没有新头像URL但有有效的现有缓存，保留现有缓存
      teacherInfo.tavatarCache = existingTeacher.tavatarCache;
      teacherInfo.cacheTime = existingTeacher.cacheTime;
    }
    
    teacherCache[teacherInfo.tid] = teacherInfo;
    localStorage.setItem(TEACHER_STORAGE_KEY, JSON.stringify(teacherCache));
  } catch (error) {
    console.error('保存老师信息失败:', error);
  }
};

/**
 * 从本地存储获取老师信息
 * @param teacherId 老师ID
 * @returns TeacherInfo | null
 */
export const getTeacherInfo = (teacherId: number): TeacherInfo | null => {
  try {
    const existingData = localStorage.getItem(TEACHER_STORAGE_KEY);
    if (!existingData) return null;
    const teacherCache: Record<number, TeacherInfo> = JSON.parse(existingData);
    const teacherInfo = teacherCache[teacherId];
    
    if (!teacherInfo) return null;
    
    // 检查头像缓存是否过期
    if (teacherInfo.tavatarCache && isAvatarCacheExpired(teacherInfo.cacheTime)) {
      // 缓存过期，清除缓存数据但保留其他信息
      teacherInfo.tavatarCache = null;
      teacherInfo.cacheTime = undefined;
      // 更新存储
      teacherCache[teacherId] = teacherInfo;
      localStorage.setItem(TEACHER_STORAGE_KEY, JSON.stringify(teacherCache));
    }
    
    return teacherInfo;
  } catch (error) {
    console.error('获取老师信息失败:', error);
    return null;
  }
};

/**
 * 获取老师头像（优先使用缓存）
 * @param teacherId 老师ID
 * @returns string | null 头像URL或base64数据
 */
export const getTeacherAvatar = (teacherId: number): string | null => {
  const teacherInfo = getTeacherInfo(teacherId);
  if (!teacherInfo) return null;
  
  // 优先返回缓存的base64数据
  if (teacherInfo.tavatarCache && !isAvatarCacheExpired(teacherInfo.cacheTime)) {
    return teacherInfo.tavatarCache;
  }
  
  // 如果没有缓存或缓存过期，返回原始URL（可能已过期）
  return teacherInfo.tavatar || null;
};

/**
 * 更新练习记录状态
 * @param elid 练习情况ID
 * @param status 练习状态（0：待练习；1：练习中；2：已提交）
 * @returns Promise<{message: string}> 更新响应
 */
export const updateExerciseLogStatus = async (
  elid: number,
  status: number
): Promise<{message: string}> => {
  try {
    const response = await put<{message: string}>(`/exercise/log/${elid}/status?status=${status}`);
    return response;
  } catch (error) {
    console.error('更新练习状态失败:', error);
    throw error;
  }
};