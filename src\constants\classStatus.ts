// 课程状态枚举
export enum ClassStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}

// 课程状态配置
export const CLASS_STATUS_CONFIG = {
  [ClassStatus.NOT_STARTED]: {
    text: '未开始',
    color: '#8c8c8c'
  },
  [ClassStatus.IN_PROGRESS]: {
    text: '开班中',
    color: '#21808d'
  },
  [ClassStatus.COMPLETED]: {
    text: '已结束',
    color: '#13343b'
  }
} as const;

// 月份名称映射
export const MONTH_NAMES = [
  '1月', '2月', '3月', '4月', '5月', '6月',
  '7月', '8月', '9月', '10月', '11月', '12月'
] as const;

// 日历配置
export const CALENDAR_CONFIG = {
  BUTTON_TEXT: {
    today: '今天',
    month: '月视图',
    list: '列表视图'
  },
  NO_EVENTS_TEXT: '暂无课程安排',
  DAY_MAX_EVENTS: 2,
  ASPECT_RATIO: 1.2,
  FIRST_DAY: 0, // 周日作为一周的第一天
  HEADER_TOOLBAR: {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,listMonth'
  },
  EVENT_TIME_FORMAT: {
    hour: undefined,
    minute: undefined,
    hour12: false
  },
  DAY_HEADER_FORMAT: {
    weekday: 'short' as const
  },
  TITLE_FORMAT: {
    year: 'numeric' as const,
    month: 'long' as const
  }
} as const;