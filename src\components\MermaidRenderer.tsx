import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import mermaid from 'mermaid';
import MermaidDialog from './MermaidDialog';

interface MermaidRendererProps {
  chart: string;
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ chart }) => {
  const [svg, setSvg] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [dialogVisible, setDialogVisible] = useState<boolean>(false);
  const mermaidRef = useRef<HTMLDivElement>(null);
  // 使用内容的hash作为稳定的ID，相同内容的图表使用相同ID
  const [id] = useState(() => {
    const hash = chart.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return `mermaid-${Math.abs(hash).toString(36)}`;
  });
  const renderTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRenderedChart = useRef<string>('');

  // 检查Mermaid图表是否完整
  const isMermaidComplete = (code: string): boolean => {
    // 检查基本的开始和结束标记
    if (!code.trim()) return false;

    // 检查是否有基本的图表类型定义
    const hasGraphType = /^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|stateDiagram-v2|erDiagram|journey|gantt|pie|gitGraph)/m.test(code);

    // 检查是否有明显的语法错误标记
    const hasObviousErrors = /^\s*[\w\s]+[^\s\w:;]\s*$/m.test(code);

    // 放宽对基本结构的要求，只要有图表类型定义就认为是完整的
    // 对于简单图表，可能没有这些特殊字符

    return hasGraphType && !hasObviousErrors;
  };

  // 使用useMemo缓存mermaid配置，避免每次渲染都重新初始化
  const mermaidConfig = useMemo(() => ({
    startOnLoad: true,
    theme: 'default' as const, // 使用 as const 确保类型正确
    securityLevel: 'loose' as const,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    flowchart: {
      htmlLabels: true,
      curve: 'basis',
      useMaxWidth: true,
      diagramPadding: 8,
    },
    sequence: {
      diagramMarginX: 50,
      diagramMarginY: 10,
      actorMargin: 50,
      width: 150,
      height: 65,
      boxMargin: 10,
      boxTextMargin: 5,
      noteMargin: 10,
      messageMargin: 35,
      mirrorActors: true,
      bottomMarginAdj: 1,
      useMaxWidth: true,
      rightAngles: false,
      showSequenceNumbers: false,
    },
    gantt: {
      titleTopMargin: 25,
      barHeight: 20,
      barGap: 4,
      topPadding: 50,
      leftPadding: 75,
      gridLineStartPadding: 35,
      fontSize: 11,
      fontFamily: '"Open-Sans", "sans-serif"',
      numberSectionStyles: 4,
      axisFormat: '%Y-%m-%d',
      topAxis: false,
    },
    logLevel: 1 as const, // 设置为ERROR级别，减少控制台输出
    deterministicIds: false, // 对于11.4.0版本，确保不使用确定性ID
  }), []);

  // 跟踪渲染尝试次数
  const [renderAttempts, setRenderAttempts] = useState<number>(0);
  const maxRenderAttempts = 3; // 最大渲染尝试次数

  // 使用useCallback缓存渲染函数，避免不必要的重新创建
  const renderChart = useCallback(async () => {
    if (!chart) return;

    // 清除之前的错误和渲染结果
    setError(null);

    // 清除之前的渲染超时
    if (renderTimeoutRef.current) {
      clearTimeout(renderTimeoutRef.current);
      renderTimeoutRef.current = null;
    }

    // 如果图表代码看起来不完整，显示加载状态而不是错误
    if (!isMermaidComplete(chart)) {
      // 不返回，继续尝试渲染
    }

    try {
      // 初始化mermaid配置
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mermaid.initialize(mermaidConfig as any);

      try {
        // 尝试直接渲染，避免操作DOM
        const { svg } = await mermaid.render(id, chart);
        setSvg(svg);
        // 成功渲染后重置尝试次数
        setRenderAttempts(0);
      } catch {
        // 直接渲染失败，尝试替代方法

        // 创建一个离屏容器进行渲染，减少DOM操作
        const container = document.createElement('div');
        container.style.display = 'none';
        container.innerHTML = `<div class="mermaid" id="${id}-fallback">${chart}</div>`;
        document.body.appendChild(container);

        try {
          // 使用contentLoaded方法触发渲染
          mermaid.contentLoaded();

          // 使用Promise和setTimeout结合，更可靠地处理异步渲染
          await new Promise<void>((resolve, reject) => {
            setTimeout(() => {
              try {
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                  setSvg(svgElement.outerHTML);
                  // 成功渲染后重置尝试次数
                  setRenderAttempts(0);
                  resolve();
                } else {
                  reject(new Error('SVG元素未找到'));
                }
              } catch (error) {
                reject(error);
              } finally {
                // 确保清理DOM
                if (container.parentNode) {
                  document.body.removeChild(container);
                }
              }
            }, 300); // 增加超时时间，给渲染更多时间
          });
        } catch (error) {
          // 增加渲染尝试次数
          const newAttempts = renderAttempts + 1;
          setRenderAttempts(newAttempts);

          // 如果尝试次数未达到最大值，则延迟后再次尝试渲染
          if (newAttempts < maxRenderAttempts) {
            setTimeout(() => {
              // 重新初始化mermaid并尝试渲染
              mermaid.initialize({
                ...mermaidConfig,
                startOnLoad: false as const // 确保不会自动渲染
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              } as any);
              // 强制重新渲染
              renderChart();
            }, 500);
          } else {
            throw error;
          }
        }
      }
    } catch {
      // 如果已经尝试了最大次数，显示错误
      if (renderAttempts >= maxRenderAttempts - 1) {
        setError('图表渲染失败，请检查语法');
      }
    }
  }, [chart, id, mermaidConfig, renderAttempts]);

  // 当图表内容变化时，渲染图表
  useEffect(() => {
    // 只有当图表内容真正发生变化时才重新渲染
    if (lastRenderedChart.current !== chart) {
      lastRenderedChart.current = chart;
      renderChart();
    }

    // 清理函数
    return () => {
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current);
      }
    };
  }, [chart, renderChart]);

  // 打开弹窗
  const openDialog = () => {
    setDialogVisible(true);
  };

  // 关闭弹窗
  const closeDialog = () => {
    setDialogVisible(false);
  };

  if (error) {
    return (
      <div className="mermaid-error" style={{ color: 'red', padding: '8px', backgroundColor: '#fff0f0', borderRadius: '4px' }}>
        {error}
        <pre style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f8f8f8', borderRadius: '4px', overflow: 'auto' }}>
          {chart}
        </pre>
      </div>
    );
  }

  if (svg) {
    return (
      <>
        <div
          className="mermaid-diagram"
          dangerouslySetInnerHTML={{ __html: svg }}
          style={{
            textAlign: 'center',
            margin: '16px 0 4px 0', /* 减少下边距，让后续内容更紧凑 */
            cursor: 'pointer',
            padding: '8px',
            border: '1px solid transparent',
            borderRadius: '4px',
            transition: 'border-color 0.3s'
          }}
          onClick={openDialog}
          title="点击查看大图"
          onMouseOver={(e) => e.currentTarget.style.borderColor = '#d9d9d9'}
          onMouseOut={(e) => e.currentTarget.style.borderColor = 'transparent'}
        />
        <MermaidDialog
          chart={chart}
          visible={dialogVisible}
          onClose={closeDialog}
        />
      </>
    );
  }

  return (
    <div ref={mermaidRef} className="mermaid-loading" style={{ textAlign: 'center', padding: '16px' }}>
      加载图表中...
    </div>
  );
};

export default React.memo(MermaidRenderer, (prevProps, nextProps) => {
  // 只有当chart内容真正发生变化时才重新渲染
  return prevProps.chart === nextProps.chart;
});