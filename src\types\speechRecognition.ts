// Web Speech API 类型定义
export interface SpeechRecognitionEvent {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

export interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

export interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

export interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

export interface SpeechRecognitionErrorEvent {
  error: 'no-speech' | 'aborted' | 'audio-capture' | 'network' | 'not-allowed' | 'service-not-allowed' | 'bad-grammar' | 'language-not-supported';
  message: string;
}

export interface SpeechGrammarList {
  length: number;
  item(index: number): SpeechGrammar;
  [index: number]: SpeechGrammar;
  addFromURI(src: string, weight?: number): void;
  addFromString(string: string, weight?: number): void;
}

export interface SpeechGrammar {
  src: string;
  weight: number;
}

export interface SpeechRecognitionAPI {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  serviceURI: string;
  grammars: SpeechGrammarList;
  
  start(): void;
  stop(): void;
  abort(): void;
  
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onstart: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onend: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onerror: ((this: SpeechRecognitionAPI, ev: SpeechRecognitionErrorEvent) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onresult: ((this: SpeechRecognitionAPI, ev: SpeechRecognitionEvent) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onnomatch: ((this: SpeechRecognitionAPI, ev: SpeechRecognitionEvent) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onsoundstart: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onsoundend: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onspeechstart: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onspeechend: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onaudiostart: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onaudioend: ((this: SpeechRecognitionAPI, ev: Event) => any) | null;
}

// 扩展 Window 接口
declare global {
  interface Window {
    SpeechRecognition?: new () => SpeechRecognitionAPI;
    webkitSpeechRecognition?: new () => SpeechRecognitionAPI;
  }
}