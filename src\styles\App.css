/* 全局样式重置 */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* 确保body和html没有默认边距和填充 */
html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* Ant Design 全局样式覆盖 */
.ant-layout {
  background: #f0f2f5;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-btn {
  border-radius: 6px;
}

.ant-input, .ant-input-password {
  border-radius: 6px;
}

/* 左侧侧边栏选中项背景色 */
.ant-menu-item-selected {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

.ant-menu-submenu-selected {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 左侧侧边栏列表项悬停背景色 */
.ant-menu-item:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

.ant-menu-submenu-title:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 确保在不同状态下都保持一致的选中背景色 */
.ant-menu-item-selected:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

.ant-menu-submenu-selected:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 左侧侧边栏底部用户图标样式 */
.main-layout-sider .ant-avatar {
  background-color: rgb(19 52 59) !important;
}

/* 用户信息区域悬停背景色 - 仅在展开状态下生效 */
.main-layout-sider:not(.ant-layout-sider-collapsed) .ant-dropdown-trigger:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 用户信息区域内容悬停背景色 - 仅在展开状态下生效 */
.main-layout-sider:not(.ant-layout-sider-collapsed) [style*="cursor: pointer"]:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 确保收起状态下的用户头像悬停时不改变背景色 */
.main-layout-sider.ant-layout-sider-collapsed .ant-avatar:hover {
  background-color: rgb(19, 52, 59)  !important;
}

/* 用户弹出菜单悬停背景色 */
.ant-dropdown-menu-item:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

.ant-dropdown-menu-submenu-title:hover {
  background-color: rgba(33, 128, 141, 0.15) !important;
}

/* 左侧侧边栏收起按钮样式 */
.logo-area .ant-btn {
  font-size: 16px;
  width: 32px;
  height: 32px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.logo-area .ant-btn:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
