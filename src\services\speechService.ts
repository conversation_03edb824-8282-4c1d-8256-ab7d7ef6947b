// 语音识别服务
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
  noSpeechTimeout?: number; // 无语音超时时间（毫秒）
}

export interface CountdownInfo {
  remainingSeconds: number;
  totalSeconds: number;
}

export class SpeechRecognitionService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private recognition: any = null;
  private isSupported = false;
  private isListening = false;
  private isPreparing = false; // 准备中状态
  private shouldRestart = false; // 是否应该自动重启
  private restartTimeout: NodeJS.Timeout | null = null;
  private noSpeechTimeout: NodeJS.Timeout | null = null; // 无语音超时定时器
  private countdownInterval: NodeJS.Timeout | null = null; // 倒计时定时器
  private noSpeechTimeoutDuration = 10000; // 默认10秒无语音超时
  private countdownStartTime: number = 0; // 倒计时开始时间
  private isCountingDown = false; // 是否正在倒计时
  private currentCallbacks: {
    onResult?: (result: SpeechRecognitionResult) => void;
    onError?: (error: string) => void;
    onEnd?: () => void;
    onPreparing?: () => void; // 准备中回调
    onListening?: () => void; // 开始监听回调
    onCountdown?: (info: CountdownInfo | null) => void; // 倒计时回调，null表示清除倒计时
  } = {};

  private resetNoSpeechTimeout() {
    // 清除现有的超时和倒计时
    if (this.noSpeechTimeout) {
      clearTimeout(this.noSpeechTimeout);
      this.noSpeechTimeout = null;
    }
    this.clearCountdown();

    // 只有在正常监听状态下才设置无语音超时
    if (this.isListening && this.shouldRestart && !this.isCountingDown) {
      this.noSpeechTimeout = setTimeout(() => {
        // 再次检查状态，确保仍在监听且应该重启
        if (this.isListening && this.shouldRestart && !this.isCountingDown) {
          this.startCountdown();
        }
      }, this.noSpeechTimeoutDuration);
    }
  }

  private startCountdown() {
    // 如果已经在倒计时，先清除
    if (this.isCountingDown) {
      this.clearCountdown();
    }
    
    this.isCountingDown = true;
    this.countdownStartTime = Date.now();
    const countdownDuration = 5000; // 5秒倒计时
    const totalSeconds = Math.ceil(countdownDuration / 1000);
    
    // 立即触发第一次倒计时回调
    this.currentCallbacks.onCountdown?.({
      remainingSeconds: totalSeconds,
      totalSeconds
    });

    this.countdownInterval = setInterval(() => {
      // 双重检查，确保倒计时状态正确
      if (!this.isCountingDown || !this.isListening) {
        this.clearCountdown();
        return;
      }

      const elapsed = Date.now() - this.countdownStartTime;
      const remaining = Math.max(0, countdownDuration - elapsed);
      const remainingSeconds = Math.ceil(remaining / 1000);

      if (remainingSeconds > 0) {
        this.currentCallbacks.onCountdown?.({
          remainingSeconds,
          totalSeconds
        });
      } else {
        // 倒计时结束，彻底停止识别
        this.endCountdownAndStop();
      }
    }, 1000);
  }

  private clearCountdown() {
    this.isCountingDown = false;
    this.countdownStartTime = 0;
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
      this.countdownInterval = null;
    }
  }

  private endCountdownAndStop() {
    // 清除倒计时
    this.clearCountdown();
    
    // 设置标志，防止重启
    this.shouldRestart = false;
    this.isListening = false;
    this.isPreparing = false;
    
    // 清除所有超时
    this.clearTimeouts();
    
    // 停止识别
    if (this.recognition) {
      try {
        this.recognition.stop();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        // 忽略停止时的错误
      }
    }
    
    // 通知UI
    this.currentCallbacks.onCountdown?.(null);
    this.currentCallbacks.onEnd?.();
  }

  private clearTimeouts() {
    if (this.restartTimeout) {
      clearTimeout(this.restartTimeout);
      this.restartTimeout = null;
    }
    if (this.noSpeechTimeout) {
      clearTimeout(this.noSpeechTimeout);
      this.noSpeechTimeout = null;
    }
    this.clearCountdown();
  }

  constructor() {
    this.initializeRecognition();
  }

  private initializeRecognition() {
    // 检查浏览器是否支持语音识别
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      this.isSupported = true;
      this.recognition = new SpeechRecognition();
      
      // 设置默认配置
      this.recognition.continuous = true; // 改为连续识别
      this.recognition.interimResults = true;
      this.recognition.lang = 'zh-CN';
      this.recognition.maxAlternatives = 1;
    }
  }

  public isRecognitionSupported(): boolean {
    return this.isSupported;
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  public isCurrentlyPreparing(): boolean {
    return this.isPreparing;
  }

  private setupRecognitionHandlers() {
    if (!this.recognition) return;

    // 设置事件处理器
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.recognition.onresult = (event: any) => {
      const result = event.results[event.results.length - 1];
      const transcript = result[0].transcript;
      const confidence = result[0].confidence;
      const isFinal = result.isFinal;
      
      if (this.isCountingDown) {
        this.clearCountdown();
        // 通知UI清除倒计时状态
        this.currentCallbacks.onCountdown?.(null);
      }
      this.resetNoSpeechTimeout();

      this.currentCallbacks.onResult?.({
        transcript,
        confidence,
        isFinal
      });
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.recognition.onerror = (event: any) => {
      let errorMessage = '语音识别出错';
      
      switch (event.error) {
        case 'no-speech':
          // 如果正在倒计时，不要重启，让倒计时自然结束
          if (this.isCountingDown) {
            return;
          }
          // 对于no-speech错误，如果应该重启，则自动重启
          if (this.shouldRestart && this.isListening) {
            this.restartRecognition();
            return;
          }
          errorMessage = '未检测到语音输入';
          break;
        case 'audio-capture':
          errorMessage = '无法访问麦克风';
          break;
        case 'not-allowed':
          errorMessage = '麦克风权限被拒绝';
          break;
        case 'network':
          errorMessage = '网络连接错误';
          break;
        case 'aborted':
          // 主动停止时不显示错误
          return;
        default:
          errorMessage = `语音识别错误: ${event.error}`;
      }
      
      this.isListening = false;
      this.isPreparing = false;
      this.shouldRestart = false;
      this.clearTimeouts();
      this.currentCallbacks.onError?.(errorMessage);
    };

    this.recognition.onend = () => {
      // 如果正在倒计时，不要重启，让倒计时自然结束
      if (this.isCountingDown) {
        return;
      }
      
      // 如果应该重启且仍在监听状态，则自动重启
      if (this.shouldRestart && this.isListening) {
        this.restartRecognition();
      } else {
        this.isListening = false;
        this.isPreparing = false;
        this.shouldRestart = false;
        this.clearTimeouts();
        this.currentCallbacks.onEnd?.();
      }
    };

    this.recognition.onstart = () => {
      this.isPreparing = false;
      this.isListening = true;
      this.resetNoSpeechTimeout();
      this.currentCallbacks.onListening?.();
    };
  }

  private restartRecognition() {
    if (this.restartTimeout) {
      clearTimeout(this.restartTimeout);
    }
    
    // 短暂延迟后重启，避免频繁重启
    this.restartTimeout = setTimeout(() => {
      if (this.shouldRestart && this.recognition) {
        try {
          this.recognition.start();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          // 如果重启失败，停止尝试
          this.isListening = false;
          this.isPreparing = false;
          this.shouldRestart = false;
          this.clearTimeouts();
          this.currentCallbacks.onError?.('语音识别重启失败');
        }
      }
    }, 100);
  }

  public startListening(
    onResult: (result: SpeechRecognitionResult) => void,
    onError?: (error: string) => void,
    onEnd?: () => void,
    onPreparing?: () => void,
    onListening?: () => void,
    options?: SpeechRecognitionOptions,
    onCountdown?: (info: CountdownInfo | null) => void
  ): void {
    if (!this.isSupported) {
      onError?.('浏览器不支持语音识别功能');
      return;
    }

    if (this.isListening || this.isPreparing) {
      onError?.('语音识别已在进行中');
      return;
    }

    // 保存回调函数
    this.currentCallbacks = { onResult, onError, onEnd, onPreparing, onListening, onCountdown };

    // 应用配置选项
    if (options) {
      if (options.language) this.recognition.lang = options.language;
      if (options.continuous !== undefined) this.recognition.continuous = options.continuous;
      if (options.interimResults !== undefined) this.recognition.interimResults = options.interimResults;
      if (options.maxAlternatives !== undefined) this.recognition.maxAlternatives = options.maxAlternatives;
      if (options.noSpeechTimeout !== undefined) this.noSpeechTimeoutDuration = options.noSpeechTimeout;
    }

    // 设置事件处理器
    this.setupRecognitionHandlers();

    // 启用自动重启
    this.shouldRestart = true;
    
    // 进入准备状态
    this.isPreparing = true;
    this.currentCallbacks.onPreparing?.();

    // 添加短暂延迟，让用户看到准备状态
    setTimeout(() => {
      if (this.shouldRestart && this.isPreparing) {
        try {
          this.recognition.start();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          this.isListening = false;
          this.isPreparing = false;
          this.shouldRestart = false;
          this.clearTimeouts();
          onError?.('启动语音识别失败');
        }
      }
    }, 500); // 500ms延迟，让准备状态更明显
  }

  public stopListening(): void {
    this.shouldRestart = false;
    this.clearTimeouts();

    if (this.recognition && (this.isListening || this.isPreparing)) {
      this.recognition.stop();
    }
    
    this.isListening = false;
    this.isPreparing = false;
  }

  public abortListening(): void {
    this.shouldRestart = false;
    this.clearTimeouts();

    if (this.recognition && (this.isListening || this.isPreparing)) {
      this.recognition.abort();
    }
    
    this.isListening = false;
    this.isPreparing = false;
  }
}

// 创建单例实例
export const speechRecognitionService = new SpeechRecognitionService();