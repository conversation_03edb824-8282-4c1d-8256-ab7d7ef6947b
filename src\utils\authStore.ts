import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { clearAllAppStorage } from './localStorage';

// 用户信息接口
export interface User {
  uid: number;
  username: string;
  name: string;
  tenant_id: number;
  token: string;
}

// 认证状态接口
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (user: User) => void;
  logout: () => void;
}

// 创建认证状态管理
export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      login: (user: User) => {
        set({ user, isAuthenticated: true });
      },
      logout: () => {
        // 清除认证状态
        set({ user: null, isAuthenticated: false });
        
        // 清除所有应用相关的localStorage状态（保留认证信息，因为zustand会自动处理）
        clearAllAppStorage(true);
      },
    }),
    {
      name: 'auth-storage',
    }
  )
);