import React from 'react';
import { Drawer, Space, Typography, Empty } from 'antd';
import { CompassOutlined } from '@ant-design/icons';
import './GuideDrawer.css';

const { Title } = Typography;

// Helper function to process content that may contain HTML or plain text with newlines
const processContent = (content: string): string => {
  // Check if content contains HTML tags
  const hasHtmlTags = /<[^>]*>/g.test(content);
  
  if (hasHtmlTags) {
    // If it contains HTML tags, return as is
    return content;
  } else {
    // If it's plain text, convert newlines to <br> tags
    return content.replace(/\n/g, '<br>');
  }
};

export interface GuideItem {
  title: string;
  details: string;
}

export interface GuideDrawerProps {
  visible: boolean;
  onClose: () => void;
  guides: GuideItem[];
  title?: string;
  width?: string | number;
}

const GuideDrawer: React.FC<GuideDrawerProps> = ({
  visible,
  onClose,
  guides,
  title = '练习指南',
  width = '65%'
}) => {
  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CompassOutlined style={{ color: '#21808d', fontSize: '18px' }} />
          <span>{title}</span>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={width}
      className="guide-drawer"
    >
      <div className="drawer-content">
        {guides && guides.length > 0 ? (
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {guides.map((guide, index) => (
              <div key={index} className="guide-step">
                <div className="guide-step-header">
                  <Title level={4} className="guide-step-title">
                    <span className="guide-step-number">{index + 1}</span>
                    {guide.title}
                  </Title>
                </div>
                <div 
                  className="guide-step-content"
                  dangerouslySetInnerHTML={{ __html: processContent(guide.details) }}
                />
              </div>
            ))}
          </Space>
        ) : (
          <Empty description="暂无练习指南" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
    </Drawer>
  );
};

export default GuideDrawer;