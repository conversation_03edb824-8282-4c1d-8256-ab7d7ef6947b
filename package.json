{"name": "ai-tmai-tcamp-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@types/lodash": "^4.17.16", "@types/react-syntax-highlighter": "^15.5.13", "@xyflow/react": "^12.8.1", "antd": "^5.22.5", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "mermaid": "^11.4.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.1.1", "react-scrollbars-custom": "^4.1.1", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.0.5", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}