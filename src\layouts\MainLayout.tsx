import React, { useState, useEffect } from 'react';
import { Layout, Menu, Typography, Avatar, Dropdown, Button, ConfigProvider } from 'antd';
import type { MenuProps } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  BookOutlined, // 学习中心图标
  SettingOutlined, // 用户菜单需要
  RocketOutlined, // 训练营图标
} from '@ant-design/icons';
import { useAuthStore } from '../utils/authStore';
import { useNavigate, useLocation } from 'react-router-dom';
import { logout } from '../services/auth';
import '../styles/MainLayout.css';

const { Sider, Content } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(() => {
    const savedCollapsed = localStorage.getItem('sidebarCollapsed');
    return savedCollapsed ? JSON.parse(savedCollapsed) : false;
  });

  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(collapsed));
  }, [collapsed]);
  const { user, logout: clearAuth } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = async () => {
    try {
      await logout();
      clearAuth();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // 即使登出接口失败，也清除本地状态
      clearAuth();
      navigate('/login');
    }
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const sidebarMenuItems: MenuProps['items'] = [
    {
      key: '/dashboard',
      icon: <BookOutlined />,
      label: '学习中心',
      onClick: () => {
        navigate('/dashboard');
      },
    },
    {
      key: '/training-camp',
      icon: <RocketOutlined />,
      label: '训练营',
      onClick: () => {
        navigate('/training-camp');
      },
    },
  ];

  return (
    <>
      <ConfigProvider
      theme={{
        components: {
          Menu: {
            itemColor: '#000000', // 菜单项文字颜色为黑色
            itemSelectedBg: '#f5f5f5', // 选中项背景色为灰色
            itemSelectedColor: '#000000', // 选中项文字颜色为黑色
            itemHoverBg: '#f5f5f5', // 悬停背景色为灰色
            itemHoverColor: '#000000', // 悬停文字颜色为黑色
            iconSize: collapsed ? 24 : 18, // 根据收起状态动态调整图标大小
          },
        },
      }}
    >
      <Layout className="main-layout">
        {/* 左侧边栏 */}
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="main-layout-sider"
          width={240}
          collapsedWidth={80}
        >
          {/* Logo区域 */}
          <div
            className={`logo-area ${collapsed ? 'logo-area-collapsed' : 'logo-area-expanded'}`}
            onClick={collapsed ? () => setCollapsed(false) : undefined}
          >
            <div className={`logo-inner ${collapsed ? 'logo-inner-collapsed' : 'logo-inner-expanded'}`}>
              <img
                src="/logo.png"
                alt="锤磨AI"
                className={collapsed ? "logo-img-collapsed" : "logo-img"}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <Title level={4} className={`logo-title ${collapsed ? 'logo-title-hidden' : ''}`}>
                锤磨AI
              </Title>
            </div>

          {/* 折叠按钮 - 只在展开状态显示 */}
          {!collapsed && (
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={() => setCollapsed(true)}
              style={{
                fontSize: '16px',
                width: 32,
                height: 32,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />
          )}
        </div>

        {/* 菜单区域 */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          paddingBottom: collapsed ? '80px' : '100px', // 为底部用户信息区域留出空间
        }}>
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{
              fontSize: '16px',
              fontWeight: 500,
              padding: collapsed ? '0px 16px' : '0px 4px',
              borderRight: 0,
              marginTop: 8,
              backgroundColor: 'transparent',
            }}
            theme="light"
            items={sidebarMenuItems}
          />
        </div>

        {/* 用户信息区域 - 贴近屏幕底部 */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: '1px solid #f0f0f0',
          padding: collapsed ? '12px 0' : '12px 16px',
          background: '#fff',
          zIndex: 10,
        }}>
          {!collapsed ? (
            <Dropdown menu={{ items: userMenuItems }} placement="topRight" trigger={['click']}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px',
                borderRadius: '6px',
                cursor: 'pointer',
                transition: 'background-color 0.2s',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}>
                <Avatar
                  size={32}
                  style={{ backgroundColor: '#262626', marginRight: 12 }}
                  icon={<UserOutlined />}
                />
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: 500,
                    color: '#262626',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {user?.name || user?.username}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#8c8c8c',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {user?.username}
                  </div>
                </div>
              </div>
            </Dropdown>
          ) : (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Dropdown menu={{ items: userMenuItems }} placement="topRight" trigger={['click']}>
                <Avatar
                  size={40}
                  style={{ backgroundColor: '#000000', cursor: 'pointer' }}
                  icon={<UserOutlined />}
                />
              </Dropdown>
            </div>
          )}
        </div>
      </Sider>

      {/* 右侧内容区域 */}
      <Layout className="main-content-layout" style={{
        marginLeft: collapsed ? 80 : 240,
        height: '100vh',
        overflow: 'hidden',
      }}>
        {/* 主内容区域 */}
        <Content style={{
          background: '#f0f2f5',
          overflow: 'auto',
          height: '100vh',
        }}>
          <div style={{ padding: '24px' }}>
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
    </ConfigProvider>
    </>
  );
};

export default MainLayout;
