import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Typography, Spin, message, Empty, Tag, Avatar, Row, Col, Divider } from 'antd';
import { CalendarOutlined, BookOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { MainLayout } from '../layouts';
import { getClasses, type ClassInfo } from '../services/class';
import { useNavigate } from 'react-router-dom';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import type { EventClickArg, EventHoveringArg } from '@fullcalendar/core';
import { ClassStatus, CLASS_STATUS_CONFIG, MONTH_NAMES, CALENDAR_CONFIG } from '../constants/classStatus';
import '../styles/TrainingCamp.css';

const { Title, Text } = Typography;

// 按月分组的课程数据结构
interface MonthlyClasses {
  [monthKey: string]: {
    month: string;
    year: number;
    classes: ClassInfo[];
  };
}

// 日历事件类型定义
interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  extendedProps: {
    description: string;
    status: string;
    classData: ClassInfo;
  };
}

const TrainingCamp: React.FC = () => {
  const [classes, setClasses] = useState<ClassInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [, setMonthlyClasses] = useState<MonthlyClasses>({});
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<string>(''); // 当前选中的月份
  const [currentMonthClasses, setCurrentMonthClasses] = useState<ClassInfo[]>([]);
  const [otherClasses, setOtherClasses] = useState<ClassInfo[]>([]);
  const navigate = useNavigate();
  const calendarRef = useRef<FullCalendar>(null);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // 获取课程状态
  const getClassStatus = useCallback((btime: string, etime: string): { status: ClassStatus; text: string; color: string } => {
    const now = new Date();
    const startTime = new Date(btime);
    const endTime = new Date(etime);
    
    if (now < startTime) {
      const config = CLASS_STATUS_CONFIG[ClassStatus.NOT_STARTED];
      return { status: ClassStatus.NOT_STARTED, ...config };
    } else if (now >= startTime && now <= endTime) {
      const config = CLASS_STATUS_CONFIG[ClassStatus.IN_PROGRESS];
      return { status: ClassStatus.IN_PROGRESS, ...config };
    } else {
      const config = CLASS_STATUS_CONFIG[ClassStatus.COMPLETED];
      return { status: ClassStatus.COMPLETED, ...config };
    }
  }, []);

  // 计算课程持续天数
  const getClassDuration = useCallback((btime: string, etime: string): number => {
    const startTime = new Date(btime);
    const endTime = new Date(etime);
    const diffTime = Math.abs(endTime.getTime() - startTime.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }, []);

  // 获取月份名称
  const getMonthName = useCallback((month: number): string => {
    return MONTH_NAMES[month - 1] || '';
  }, []);

  // 检查课程是否与指定月份有交集
  const hasIntersectionWithMonth = useCallback((btime: string, etime: string, targetYear: number, targetMonth: number): boolean => {
    const classStart = new Date(btime);
    const classEnd = new Date(etime);
    
    // 目标月份的开始和结束时间
    const monthStart = new Date(targetYear, targetMonth - 1, 1);
    const monthEnd = new Date(targetYear, targetMonth, 0, 23, 59, 59);
    
    // 检查是否有交集：课程开始时间 <= 月份结束时间 && 课程结束时间 >= 月份开始时间
    return classStart <= monthEnd && classEnd >= monthStart;
  }, []);

  // 根据选中月份过滤课程
  const filterClassesBySelectedMonth = useCallback((classList: ClassInfo[], selectedMonthKey: string) => {
    if (!selectedMonthKey) {
      // 如果没有选中月份，默认使用当前月份
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      selectedMonthKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
    }
    
    const [yearStr, monthStr] = selectedMonthKey.split('-');
    const targetYear = parseInt(yearStr);
    const targetMonth = parseInt(monthStr);
    
    const currentMonth: ClassInfo[] = [];
    const others: ClassInfo[] = [];
    
    classList.forEach(classItem => {
      if (hasIntersectionWithMonth(classItem.btime, classItem.etime, targetYear, targetMonth)) {
        currentMonth.push(classItem);
      } else {
        others.push(classItem);
      }
    });
    
    setCurrentMonthClasses(currentMonth);
    setOtherClasses(others);
  }, [hasIntersectionWithMonth]);

  // 按月分组课程
  const groupClassesByMonth = useCallback((classList: ClassInfo[]): MonthlyClasses => {
    const grouped: MonthlyClasses = {};
    
    classList.forEach(classItem => {
      const startDate = new Date(classItem.btime);
      const year = startDate.getFullYear();
      const month = startDate.getMonth() + 1;
      const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
      
      if (!grouped[monthKey]) {
        grouped[monthKey] = {
          month: getMonthName(month),
          year,
          classes: []
        };
      }
      
      grouped[monthKey].classes.push(classItem);
    });
    
    return grouped;
  }, [getMonthName]);

  // 将课程数据转换为日历事件
  const convertClassesToEvents = useCallback((classList: ClassInfo[]): CalendarEvent[] => {
    return classList.map(classItem => {
      const statusInfo = getClassStatus(classItem.btime, classItem.etime);
      return {
        id: classItem.id.toString(),
        title: classItem.name,
        start: classItem.btime,
        end: classItem.etime,
        backgroundColor: statusInfo.color,
        borderColor: statusInfo.color,
        textColor: '#ffffff',
        extendedProps: {
          description: classItem.description || '',
          status: statusInfo.text,
          classData: classItem
        }
      };
    });
  }, [getClassStatus]);

  // 处理日历事件点击
  const handleEventClick = useCallback((clickInfo: EventClickArg) => {
    const classData = clickInfo.event.extendedProps.classData as ClassInfo;
    navigate(`/class/${classData.id}/exercises`);
  }, [navigate, getClassStatus, getClassDuration]);

  // 处理鼠标进入事件
  const handleEventMouseEnter = useCallback((info: EventHoveringArg) => {
    info.el.style.cursor = 'pointer';
    info.el.style.opacity = '0.8';
  }, []);

  // 处理鼠标离开事件
  const handleEventMouseLeave = useCallback((info: EventHoveringArg) => {
    info.el.style.opacity = '1';
  }, []);

  // 处理日历月份变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleCalendarMonthChange = useCallback((dateInfo: { start: Date; end: Date; view: any }) => {
    // 使用view.currentStart来获取当前显示月份的第一天
    const currentDate = dateInfo.view.currentStart;
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
    setSelectedMonth(monthKey);
    filterClassesBySelectedMonth(classes, monthKey);
  }, [classes, filterClassesBySelectedMonth]);

  // 获取课程数据
  const fetchClasses = useCallback(async () => {
    try {
      setLoading(true);
      const classList = await getClasses();
      setClasses(classList);
      const grouped = groupClassesByMonth(classList);
      setMonthlyClasses(grouped);
      const events = convertClassesToEvents(classList);
      setCalendarEvents(events);
      
      // 初始化选中月份为当前月份
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      const currentMonthKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
      setSelectedMonth(currentMonthKey);
      filterClassesBySelectedMonth(classList, currentMonthKey);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('获取课程列表失败:', error);
      
      let errorMessage = '获取课程列表失败，请稍后重试';
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          errorMessage = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMessage = '没有权限访问课程信息';
        } else if (status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络设置';
      }
      
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [convertClassesToEvents, groupClassesByMonth, filterClassesBySelectedMonth]);

  useEffect(() => {
    fetchClasses();
  }, []);

  // 监听窗口大小变化，更新日历尺寸
  useEffect(() => {
    const handleResize = () => {
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        calendarApi.updateSize();
      }
    };

    window.addEventListener('resize', handleResize);
    
    // 使用MutationObserver监听DOM变化（如侧边栏展开/收起）
    const observer = new MutationObserver(() => {
      // 延迟执行以确保DOM变化完成
      setTimeout(handleResize, 100);
    });

    // 监听body的class变化（通常侧边栏状态会改变body的class）
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    // 监听主要容器的变化
    const mainContainer = document.querySelector('.ant-layout');
    if (mainContainer) {
      observer.observe(mainContainer, {
        attributes: true,
        attributeFilter: ['class', 'style'],
        subtree: true
      });
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, []);

  // 渲染课程卡片
  const renderClassCard = useCallback((classItem: ClassInfo) => {
    const statusInfo = getClassStatus(classItem.btime, classItem.etime);
    const duration = getClassDuration(classItem.btime, classItem.etime);
    
    return (
      <Card
        key={classItem.id}
        hoverable
        style={{ 
          height: '100%',
          cursor: 'pointer',
          borderRadius: '8px',
          backgroundColor: '#ffffff'
        }}
        className="training-camp-card"
        styles={{ body: { padding: '20px' } }}
        onClick={() => navigate(`/class/${classItem.id}/exercises`)}
        cover={
          classItem.pic ? (
            <div style={{ height: '180px', overflow: 'hidden', borderRadius: '8px 8px 0 0' }}>
              <img
                alt={classItem.name}
                src={classItem.pic}
                style={{ 
                  width: '100%', 
                  height: '100%', 
                  objectFit: 'cover',
                  transition: 'transform 0.3s ease'
                }}
              />
            </div>
          ) : (
            <div
              style={{
                height: '180px',
                background: 'linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '8px 8px 0 0'
              }}
            >
              <Avatar 
                size={64} 
                icon={<BookOutlined />} 
                style={{ 
                  backgroundColor: '#d9d9d9',
                  color: '#8c8c8c'
                }} 
              />
            </div>
          )
        }
      >
        <div style={{ marginBottom: '12px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
            <Title level={4} style={{ margin: 0, fontSize: '16px', fontWeight: 600, color: '#262626' }}>
              {classItem.name}
            </Title>
            <Tag color={statusInfo.color} style={{ margin: 0, fontSize: '12px' }}>
              {statusInfo.text}
            </Tag>
          </div>
          
          <Text type="secondary" style={{ 
            fontSize: '14px', 
            lineHeight: '1.5',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}>
            {classItem.description || '暂无课程描述'}
          </Text>
        </div>
        
        <Divider style={{ margin: '12px 0' }} />
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', color: '#8c8c8c', fontSize: '12px' }}>
            <CalendarOutlined style={{ marginRight: '4px' }} />
            <span>{formatDate(classItem.btime)} - {formatDate(classItem.etime)}</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', color: '#8c8c8c', fontSize: '12px' }}>
            <ClockCircleOutlined style={{ marginRight: '4px' }} />
            <span>{duration}天</span>
          </div>
        </div>
      </Card>
    );
  }, [navigate]);

  return (
    <MainLayout>
      <div style={{ maxWidth: 1400, margin: '0 auto', padding: '0' }}>
        <div style={{ marginBottom: '32px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
            <div>
              <Title level={2} style={{ margin: 0, color: '#262626', fontSize: '28px', fontWeight: 600 }}>
                我的训练营
              </Title>
              <Text type="secondary" style={{ fontSize: '16px', marginTop: '8px', display: 'block' }}>
                以下是您所参加的课程，点击课程开始练习
              </Text>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="loading-container" style={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            minHeight: '60vh'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">正在加载课程信息...</Text>
            </div>
          </div>
        ) : classes.length === 0 ? (
          <div className="empty-container">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无课程信息"
            />
          </div>
        ) : (
          <Row gutter={[32, 32]}>
            {/* 左侧课程列表 */}
            <Col xs={24} lg={14} xl={16}>
              <div>
                {/* 当前选中月份的课程 */}
                {currentMonthClasses.length > 0 && (
                  <div style={{ marginBottom: '48px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '24px',
                      padding: '16px 20px',
                      background: 'rgba(19, 52, 59, 0.9)',
                      borderRadius: '12px',
                      boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)'
                    }}>
                      <Title level={3} style={{ 
                        margin: 0,
                        fontSize: '20px',
                        fontWeight: 600,
                        color: '#ffffff'
                      }}>
                        {selectedMonth ? (
                          (() => {
                            const [yearStr, monthStr] = selectedMonth.split('-');
                            const year = parseInt(yearStr);
                            const month = parseInt(monthStr);
                            return `${year}年${getMonthName(month)}`;
                          })()
                        ) : '当前月份'}
                      </Title>
                      <div style={{ 
                        marginLeft: '16px',
                        padding: '6px 14px',
                        background: 'rgba(255, 255, 255, 0.2)',
                        borderRadius: '20px',
                        fontSize: '13px',
                        color: '#ffffff',
                        fontWeight: 500,
                        backdropFilter: 'blur(10px)'
                      }}>
                        {currentMonthClasses.length} 个课程
                      </div>
                    </div>
                    
                    <Row gutter={[16, 16]}>
                      {currentMonthClasses.map(classItem => (
                        <Col key={classItem.id} xs={24} sm={12} xl={8}>
                          {renderClassCard(classItem)}
                        </Col>
                      ))}
                    </Row>
                  </div>
                )}
                
                {/* 其他课程 */}
                {otherClasses.length > 0 && (
                  <div style={{ marginBottom: '48px' }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '24px',
                      padding: '16px 20px',
                      background: 'rgba(19, 52, 59, 0.7)',
                      borderRadius: '12px',
                      boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)'
                    }}>
                      <Title level={3} style={{ 
                        margin: 0,
                        fontSize: '20px',
                        fontWeight: 600,
                        color: '#ffffff'
                      }}>
                        其他月份
                      </Title>
                      <div style={{ 
                        marginLeft: '16px',
                        padding: '6px 14px',
                        background: 'rgba(255, 255, 255, 0.2)',
                        borderRadius: '20px',
                        fontSize: '13px',
                        color: '#ffffff',
                        fontWeight: 500,
                        backdropFilter: 'blur(10px)'
                      }}>
                        {otherClasses.length} 个课程
                      </div>
                    </div>
                    
                    <Row gutter={[16, 16]}>
                      {otherClasses.map(classItem => (
                        <Col key={classItem.id} xs={24} sm={12} xl={8}>
                          {renderClassCard(classItem)}
                        </Col>
                      ))}
                    </Row>
                  </div>
                )}
              </div>
            </Col>
            
            {/* 右侧日历 */}
            <Col xs={24} lg={10} xl={8}>
              <Card>
                <FullCalendar
                  ref={calendarRef}
                  plugins={[dayGridPlugin, interactionPlugin, listPlugin]}
                  initialView="dayGridMonth"
                  events={calendarEvents}
                  eventClick={handleEventClick}
                  height="auto"
                  headerToolbar={{
                    left: 'title',
                    center: '',
                    right: 'prev,next today'
                  }}
                  locale="zh-cn"
                  eventDisplay="block"
                  dayMaxEvents={CALENDAR_CONFIG.DAY_MAX_EVENTS}
                  moreLinkClick="popover"
                  displayEventTime={false}
                  buttonText={CALENDAR_CONFIG.BUTTON_TEXT}
                  noEventsText={CALENDAR_CONFIG.NO_EVENTS_TEXT}
                  eventMouseEnter={handleEventMouseEnter}
                  eventMouseLeave={handleEventMouseLeave}
                  dayHeaderFormat={CALENDAR_CONFIG.DAY_HEADER_FORMAT}
                  titleFormat={CALENDAR_CONFIG.TITLE_FORMAT}
                  firstDay={CALENDAR_CONFIG.FIRST_DAY}
                  aspectRatio={CALENDAR_CONFIG.ASPECT_RATIO}
                  datesSet={handleCalendarMonthChange}
                  windowResizeDelay={100}
                />
              </Card>
            </Col>
          </Row>
        )}
      </div>
    </MainLayout>
  );
};

export default TrainingCamp;