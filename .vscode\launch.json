{"version": "0.2.0", "configurations": [{"name": "Launch Edge", "type": "msedge", "request": "launch", "url": "http://localhost:5173/login", "webRoot": "${workspaceFolder}", "preLaunchTask": "start-dev-server", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/src/*", "/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "/*": "${webRoot}/*", "/./~/*": "${webRoot}/node_modules/*"}, "runtimeArgs": ["--disable-web-security", "--user-data-dir=${workspaceFolder}/.vscode/edge-debug"]}, {"name": "Attach to Edge", "type": "msedge", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}", "sourceMaps": true}]}