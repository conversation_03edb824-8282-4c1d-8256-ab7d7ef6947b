import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, Tooltip, App } from 'antd';
import { ZoomInOutlined, ZoomOutOutlined, DownloadOutlined } from '@ant-design/icons';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import mermaid from 'mermaid';

interface MermaidDialogProps {
  chart: string;
  visible: boolean;
  onClose: () => void;
}

const MermaidDialog: React.FC<MermaidDialogProps> = ({ chart, visible, onClose }) => {
  // 使用App组件的message API
  const { message } = App.useApp();
  const [activeTab, setActiveTab] = useState<string>('preview');
  const [scale, setScale] = useState<number>(2.0); // 默认缩放比例为200%
  const [svgContent, setSvgContent] = useState<string>('');
  const [renderKey, setRenderKey] = useState<number>(0); // 用于强制重新渲染
  const previewRef = useRef<HTMLDivElement>(null);
  const [id] = useState(`mermaid-${Math.random().toString(36).substring(2, 11)}`);

  // 当弹窗关闭时重置状态
  useEffect(() => {
    if (!visible) {
      // 当弹窗关闭时增加renderKey，下次打开时强制重新渲染
      setRenderKey(prev => prev + 1);
    }
  }, [visible]);

  // 检查Mermaid图表是否完整
  const isMermaidComplete = (code: string): boolean => {
    // 检查基本的开始和结束标记
    if (!code.trim()) return false;

    // 检查是否有基本的图表类型定义
    const hasGraphType = /^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|stateDiagram-v2|erDiagram|journey|gantt|pie|gitGraph)/m.test(code);

    // 检查是否有明显的语法错误标记
    const hasObviousErrors = /^\s*[\w\s]+[^\s\w:;]\s*$/m.test(code);

    // 放宽对基本结构的要求，只要有图表类型定义就认为是完整的
    // 对于简单图表，可能没有这些特殊字符

    return hasGraphType && !hasObviousErrors;
  };

  // 初始化Mermaid并渲染图表
  useEffect(() => {
    // 当弹窗可见时立即渲染，不管当前标签页
    if (visible) {
      // 清除之前的内容，防止闪烁
      setSvgContent('');

      try {
        mermaid.initialize({
          startOnLoad: true,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: 14,
          flowchart: {
            htmlLabels: true,
            curve: 'basis',
            useMaxWidth: true,
            diagramPadding: 8,
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false,
          },
          gantt: {
            titleTopMargin: 25,
            barHeight: 20,
            barGap: 4,
            topPadding: 50,
            leftPadding: 75,
            gridLineStartPadding: 35,
            fontSize: 11,
            numberSectionStyles: 4,
            axisFormat: '%Y-%m-%d',
            topAxis: false,
          },
          logLevel: 1, // 设置为ERROR级别，减少控制台输出
          deterministicIds: false, // 对于11.4.0版本，确保不使用确定性ID
        });

        // 检查图表是否完整
        if (!isMermaidComplete(chart)) {
          console.log('对话框中图表代码可能不完整，但仍然尝试渲染:', chart);
          // 不返回，继续尝试渲染
        }

        // 添加小延迟确保DOM已经准备好
        setTimeout(() => {
          try {
            // 先清除之前的所有mermaid元素，避免冲突
            document.querySelectorAll('.mermaid').forEach(el => {
              if (el.id !== `mermaid-${id}-${renderKey}` && el.id !== `mermaid-${id}-${renderKey}-fallback`) {
                el.remove();
              }
            });

            mermaid.render(`mermaid-${id}-${renderKey}`, chart).then(({ svg }) => {
              setSvgContent(svg);
            }).catch(renderError => {
              console.error('Mermaid渲染错误:', renderError);

              // 尝试使用替代方法
              const container = document.createElement('div');
              container.style.display = 'none';
              document.body.appendChild(container);
              container.innerHTML = `<div class="mermaid" id="mermaid-${id}-${renderKey}-fallback">${chart}</div>`;

              try {
                // 使用替代方法，避免使用已弃用的init方法
                mermaid.contentLoaded();

                // 增加超时时间，给渲染更多时间
                setTimeout(() => {
                  try {
                    const svgElement = container.querySelector('svg');
                    if (svgElement) {
                      setSvgContent(svgElement.outerHTML);
                      console.log('对话框替代方法渲染成功');
                    } else {
                      // 如果没有找到SVG元素，尝试再次渲染
                      console.log('对话框SVG元素未找到，尝试再次渲染');

                      // 尝试第三种方法：使用mermaidAPI直接渲染
                      const newId = `mermaid-${id}-${renderKey}-third-attempt`;
                      const newContainer = document.createElement('div');
                      newContainer.style.display = 'none';
                      document.body.appendChild(newContainer);
                      newContainer.innerHTML = `<pre class="mermaid" id="${newId}">${chart}</pre>`;

                      try {
                        // 重新初始化mermaid
                        mermaid.initialize({
                          startOnLoad: false,
                          theme: 'default',
                          securityLevel: 'loose',
                          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                          fontSize: 14,
                          flowchart: {
                            htmlLabels: true,
                            curve: 'basis',
                            useMaxWidth: true,
                            diagramPadding: 8,
                          },
                          sequence: {
                            diagramMarginX: 50,
                            diagramMarginY: 10,
                            actorMargin: 50,
                            width: 150,
                            height: 65,
                            boxMargin: 10,
                            boxTextMargin: 5,
                            noteMargin: 10,
                            messageMargin: 35,
                            mirrorActors: true,
                            bottomMarginAdj: 1,
                            useMaxWidth: true,
                            rightAngles: false,
                            showSequenceNumbers: false,
                          },
                          gantt: {
                            titleTopMargin: 25,
                            barHeight: 20,
                            barGap: 4,
                            topPadding: 50,
                            leftPadding: 75,
                            gridLineStartPadding: 35,
                            fontSize: 11,
                            numberSectionStyles: 4,
                            axisFormat: '%Y-%m-%d',
                            topAxis: false,
                          },
                          logLevel: 1,
                          deterministicIds: false,
                        });

                        // 使用mermaidAPI直接渲染
                        mermaid.contentLoaded();

                        setTimeout(() => {
                          const newSvgElement = newContainer.querySelector('svg');
                          if (newSvgElement) {
                            setSvgContent(newSvgElement.outerHTML);
                            console.log('对话框第三种方法渲染成功');
                          } else {
                            // 所有方法都失败，显示错误信息
                            setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表渲染失败，请检查语法</span></div>');
                          }
                          document.body.removeChild(newContainer);
                        }, 200);
                      } catch (thirdError) {
                        console.error('对话框第三种方法失败:', thirdError);
                        if (newContainer.parentNode) {
                          document.body.removeChild(newContainer);
                        }
                        // 使用更友好的错误提示，不要显示toast
                        setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表渲染失败，请检查语法</span></div>');
                      }
                    }
                  } catch (timeoutError) {
                    console.error('对话框超时处理错误:', timeoutError);
                    // 使用更友好的错误提示，不要显示toast
                    setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表渲染失败，请检查语法</span></div>');
                  } finally {
                    if (container.parentNode) {
                      document.body.removeChild(container);
                    }
                  }
                }, 200);
              } catch (initError) {
                console.error('对话框替代方法失败:', initError);
                // 使用更友好的错误提示，不要显示toast
                setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表渲染失败，请检查语法</span></div>');
                if (container.parentNode) {
                  document.body.removeChild(container);
                }
              }
            });
          } catch (error) {
            console.error('Mermaid渲染错误:', error);
            // 使用更友好的错误提示，不要显示toast
            setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表渲染失败，请检查语法</span></div>');
          }
        }, 100);
      } catch (error) {
        console.error('Mermaid初始化错误:', error);
        setSvgContent('<div style="display: flex; justify-content: center; align-items: center; height: 100px; color: #ff4d4f;"><span>图表初始化失败</span></div>');
      }
    }
  }, [visible, chart, id, renderKey, activeTab]);

  // 放大图表
  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.25, 5)); // 允许放大到500%，每次增加25%
  };

  // 缩小图表
  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.25, 0.5)); // 每次减小25%
  };

  // 下载PNG
  const downloadPng = () => {
    if (!previewRef.current) return;

    try {
      const svgElement = previewRef.current.querySelector('svg');
      if (!svgElement) {
        message.error('未找到SVG元素');
        return;
      }

      // 创建Canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        message.error('无法创建Canvas上下文');
        return;
      }

      // 获取原始SVG的尺寸
      const svgWidth = svgElement.viewBox.baseVal.width || svgElement.width.baseVal.value;
      const svgHeight = svgElement.viewBox.baseVal.height || svgElement.height.baseVal.value;

      // 考虑缩放比例计算新的尺寸
      const scaledWidth = svgWidth * scale;
      const scaledHeight = svgHeight * scale;

      // 创建Image
      const image = new Image();

      // 设置Image源
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // 清理并下载
      image.onload = function() {
        URL.revokeObjectURL(svgUrl);

        // 设置Canvas大小为缩放后的尺寸
        canvas.width = scaledWidth;
        canvas.height = scaledHeight;

        // 填充白色背景
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制图像，并应用缩放
        ctx.drawImage(image, 0, 0, scaledWidth, scaledHeight);

        // 转换为PNG并下载
        try {
          const pngUrl = canvas.toDataURL('image/png');
          const downloadLink = document.createElement('a');
          downloadLink.href = pngUrl;
          downloadLink.download = `mermaid-diagram-${Math.round(scale * 100)}%.png`;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);

          message.success(`图表已以 ${Math.round(scale * 100)}% 的比例保存`);
        } catch (error) {
          console.error('PNG转换错误:', error);
          message.error('PNG转换失败，可能是由于跨域限制');
        }
      };

      image.src = svgUrl;
    } catch (error) {
      console.error('下载PNG错误:', error);
      message.error('下载PNG失败');
    }
  };

  return (
    <>
      <Modal
      title="Mermaid图表"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      styles={{
        body: {
          padding: '16px',
          height: '70vh',
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'preview',
            label: '预览',
            children: (
              <div style={{ position: 'relative', height: 'calc(70vh - 110px)' }}>
                {/* 滚动容器 */}
                <div style={{ height: '100%', overflow: 'auto' }} className="mermaid-preview-container">
                  <div
                    ref={previewRef}
                    style={{
                      transform: `scale(${scale})`,
                      transformOrigin: 'top left',
                      transition: 'transform 0.3s',
                      padding: '20px',
                      display: 'inline-block',
                      minHeight: '100px', // 添加最小高度，防止内容为空时崩溃
                      minWidth: '100px'   // 添加最小宽度
                    }}
                    dangerouslySetInnerHTML={{ __html: svgContent || '<div style="display: flex; justify-content: center; align-items: center; height: 100px;"><span>加载中...</span></div>' }}
                  />
                </div>

                {/* 控件容器，放在滚动容器外部 */}
                <div style={{
                  position: 'absolute',
                  bottom: '20px',
                  right: '20px',
                  display: 'flex',
                  gap: '8px',
                  background: 'rgba(255, 255, 255, 0.9)',
                  padding: '8px',
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                  zIndex: 10
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginRight: '12px' }}>
                    <span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.65)' }}>
                      {Math.round(scale * 100)}%
                    </span>
                  </div>
                  <Tooltip title="放大">
                    <Button
                      icon={<ZoomInOutlined />}
                      onClick={zoomIn}
                      disabled={scale >= 5} // 允许放大到500%
                    />
                  </Tooltip>
                  <Tooltip title="缩小">
                    <Button
                      icon={<ZoomOutOutlined />}
                      onClick={zoomOut}
                      disabled={scale <= 0.5}
                    />
                  </Tooltip>
                  <Tooltip title="下载PNG">
                    <Button
                      icon={<DownloadOutlined />}
                      onClick={downloadPng}
                    />
                  </Tooltip>
                </div>
              </div>
            )
          },
          {
            key: 'source',
            label: '源码',
            children: (
              <div style={{ height: 'calc(70vh - 110px)', overflow: 'auto' }}>
                <SyntaxHighlighter
                  language="markdown"
                  style={vs}
                  showLineNumbers={true}
                  customStyle={{
                    fontSize: '14px',
                    lineHeight: '1.5',
                    borderRadius: '4px'
                  }}
                >
                  {chart}
                </SyntaxHighlighter>
              </div>
            )
          }
        ]}
      />
    </Modal>
    </>
  );
};

export default MermaidDialog;