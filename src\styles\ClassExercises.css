/* 练习流程容器 */
.exercises-flow {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  padding: 20px 0;
  max-width: 100%;
  justify-content: flex-start;
}

/* 练习节点容器 */
.exercise-node {
  position: relative;
  flex: 0 0 auto;
}

/* 之字形布局 - 奇数行从左到右，偶数行从右到左 */
.exercises-flow > *:nth-child(6n+1),
.exercises-flow > *:nth-child(6n+2),
.exercises-flow > *:nth-child(6n+3) {
  order: 1;
}

.exercises-flow > *:nth-child(6n+4),
.exercises-flow > *:nth-child(6n+5),
.exercises-flow > *:nth-child(6n+6) {
  order: 2;
}

/* 每行最多3个卡片 */
.exercise-node {
  width: calc(33.333% - 27px);
  min-width: 280px;
}

@media (max-width: 1200px) {
  .exercise-node {
    width: calc(50% - 20px);
  }
  
  .exercises-flow > *:nth-child(4n+1),
  .exercises-flow > *:nth-child(4n+2) {
    order: 1;
  }
  
  .exercises-flow > *:nth-child(4n+3),
  .exercises-flow > *:nth-child(4n+4) {
    order: 2;
  }
}

@media (max-width: 768px) {
  .exercise-node {
    width: 100%;
  }
  
  .exercises-flow {
    flex-direction: column;
    align-items: center;
  }
  
  .exercises-flow > * {
    order: unset !important;
  }
}

/* 连接线容器 */
.connection-line {
  position: absolute;
  z-index: 5;
  pointer-events: none;
}

/* 连接线内容 */
.line-content {
  position: relative;
  background: #d9d9d9;
  border-radius: 2px;
  overflow: hidden;
}

/* 箭头样式 */
.arrow {
  position: absolute;
  width: 0;
  height: 0;
  z-index: 10;
}

/* 水平连接线样式 */
.connection-line.horizontal {
  height: 4px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-line.horizontal .arrow {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-left: 8px solid #d9d9d9;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* 垂直连接线样式 */
.connection-line.vertical {
  width: 4px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-line.vertical .arrow {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-top: 8px solid #d9d9d9;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
}

/* 对角连接线样式 */
.connection-line.diagonal {
  transform-origin: left center;
}

.connection-line.diagonal .arrow {
  right: -6px;
  top: 50%;
  transform: translateY(-50%) rotate(var(--arrow-rotation, 0deg));
  border-left: 8px solid #d9d9d9;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* 流动动画 */
.flowing .line-content {
  background: #1890ff;
}

.flowing .arrow {
  border-left-color: #1890ff !important;
  border-top-color: #1890ff !important;
}

.flowing.horizontal .flow-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: flowRight 2s linear infinite;
}

.flowing.vertical .flow-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 20px;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: flowDown 2s linear infinite;
}

.flowing.diagonal .flow-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: flowDiagonal 2s linear infinite;
}

/* 静态连接线 */
.static .line-content {
  background: #d9d9d9;
}

.static .flow-animation {
  display: none;
}

/* 流动动画关键帧 */
@keyframes flowRight {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100% + 20px));
    opacity: 0;
  }
}

@keyframes flowDown {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100% + 20px));
    opacity: 0;
  }
}

@keyframes flowDiagonal {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100% + 20px));
    opacity: 0;
  }
}

/* 练习卡片悬停效果 */
.exercise-node .ant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

/* 禁用状态的卡片不响应悬停 */
.exercise-node .ant-card[style*="opacity: 0.6"]:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 锁定图标动画 */
.exercise-node .ant-card .anticon-lock {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}



/* 卡片内容区域优化 */
.ant-card .ant-card-body {
  padding: 18px 20px !important;
}

.exercise-node .ant-card-meta-title {
  margin-bottom: 8px !important;
}

.exercise-node .ant-card-meta-description {
  color: #666 !important;
  line-height: 1.5;
}

/* 标签组合样式 */
.exercise-node .ant-tag {
  margin: 0 0 4px 4px;
  font-size: 11px;
  padding: 2px 6px;
  line-height: 1.2;
}

/* 操作区域样式 */
.exercise-node .ant-card-actions {
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.exercise-node .ant-card-actions > li {
  margin: 8px 0;
}

/* 老师信息和时长信息样式 */
.exercise-node .ant-card-actions .ant-typography {
  color: #666 !important;
  font-size: 12px;
}

.exercise-node .ant-card-actions .anticon {
  color: #999;
}

/* 防止ReactFlow控件和文字被选中 */
.react-flow__controls,
.react-flow__controls button,
.react-flow__panel {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 防止整个ReactFlow容器内的文字被意外选中 */
.react-flow {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}