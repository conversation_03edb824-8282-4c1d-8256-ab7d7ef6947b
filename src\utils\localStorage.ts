/**
 * localStorage 管理工具
 * 提供统一的localStorage操作和清理功能
 */

// 定义应用中使用的localStorage key前缀
export const LOCAL_STORAGE_PREFIXES = {
  WORKSHEET_STATE: 'worksheet_state_',
  SIDEBAR_COLLAPSED: 'sidebarCollapsed',
  CLASS_STATE: 'class_',
  EXERCISE_STATE: 'exercise_',
  TRAINING_CAMP_STATE: 'training_camp_',
  USER_PREFERENCES: 'user_preferences_',
  CACHE: 'cache_'
} as const;

/**
 * 清除所有应用相关的localStorage项
 * @param excludeAuth 是否排除认证相关的存储（默认为true，保留认证信息）
 */
export const clearAllAppStorage = (excludeAuth: boolean = true): void => {
  const keysToRemove: string[] = [];
  
  // 遍历localStorage，找到所有需要清除的key
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (!key) continue;
    
    // 检查是否是应用相关的key
    const isAppKey = Object.values(LOCAL_STORAGE_PREFIXES).some(prefix => 
      key.startsWith(prefix)
    );
    
    // 如果排除认证信息，跳过auth-storage
    if (excludeAuth && key === 'auth-storage') {
      continue;
    }
    
    if (isAppKey) {
      keysToRemove.push(key);
    }
  }
  
  // 批量删除找到的key
  keysToRemove.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`清除localStorage项失败: ${key}`, error);
    }
  });
  
  console.log(`已清除 ${keysToRemove.length} 个localStorage项:`, keysToRemove);
};

/**
 * 清除特定前缀的localStorage项
 * @param prefix 要清除的前缀
 */
export const clearStorageByPrefix = (prefix: string): void => {
  const keysToRemove: string[] = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(prefix)) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`清除localStorage项失败: ${key}`, error);
    }
  });
  
  console.log(`已清除前缀为 "${prefix}" 的 ${keysToRemove.length} 个localStorage项:`, keysToRemove);
};

/**
 * 安全地设置localStorage项
 * @param key 键名
 * @param value 值
 */
export const setStorageItem = (key: string, value: string): boolean => {
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.warn(`设置localStorage项失败: ${key}`, error);
    return false;
  }
};

/**
 * 安全地获取localStorage项
 * @param key 键名
 * @param defaultValue 默认值
 */
export const getStorageItem = (key: string, defaultValue: string | null = null): string | null => {
  try {
    return localStorage.getItem(key) || defaultValue;
  } catch (error) {
    console.warn(`获取localStorage项失败: ${key}`, error);
    return defaultValue;
  }
};

/**
 * 安全地移除localStorage项
 * @param key 键名
 */
export const removeStorageItem = (key: string): boolean => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn(`移除localStorage项失败: ${key}`, error);
    return false;
  }
};