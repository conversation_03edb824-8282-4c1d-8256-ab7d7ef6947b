import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios';
import { useAuthStore } from '../utils/authStore';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT || '10000'),
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const { user } = useAuthStore.getState();

    // 如果有token，添加到请求头
    if (user && user.token) {
      config.headers.Authorization = `Bearer ${user.token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      // 清除认证状态
      useAuthStore.getState().logout();

      // 检查当前是否在登录页面，如果不是才重定向
      const isLoginPage = window.location.pathname.includes('/login');
      if (!isLoginPage) {
        // 重定向到登录页
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// 封装GET请求
export const get = <T>(url: string, params?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.get(url, { params, ...config }).then((res: AxiosResponse<T>) => res.data);
};

// 封装POST请求
export const post = <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.post(url, data, config).then((res: AxiosResponse<T>) => res.data);
};

// 封装PUT请求
export const put = <T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> => {
  return api.put(url, data, config)
    .then((res: AxiosResponse<T>) => res.data)
    .catch((error: unknown) => {
      throw error;
    });
};

// 封装DELETE请求
export const del = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return api.delete(url, config).then((res: AxiosResponse<T>) => res.data);
};

export default api; 