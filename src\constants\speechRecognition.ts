// 语音识别配置常量
export const SPEECH_RECOGNITION_CONFIG = {
  // 默认配置
  DEFAULT: {
    language: 'zh-CN',
    continuous: true,
    interimResults: true,
    maxAlternatives: 1,
    noSpeechTimeout: 10000, // 10秒
    preparingDelay: 500, // 准备状态延迟
    restartDelay: 100, // 重启延迟
  },
  
  // 超时配置
  TIMEOUTS: {
    NO_SPEECH: 10000,
    PREPARING: 500,
    RESTART: 100,
    CONNECTION: 5000,
  },
  
  // 错误消息映射
  ERROR_MESSAGES: {
    'no-speech': '未检测到语音输入',
    'audio-capture': '无法访问麦克风',
    'not-allowed': '麦克风权限被拒绝',
    'network': '网络连接错误',
    'aborted': '语音识别已停止',
    'service-not-allowed': '语音识别服务不可用',
    'bad-grammar': '语法错误',
    'language-not-supported': '不支持的语言',
    'default': '语音识别出错',
  },
  
  // 状态枚举
  STATES: {
    IDLE: 'idle',
    PREPARING: 'preparing',
    LISTENING: 'listening',
    PROCESSING: 'processing',
    ERROR: 'error',
  } as const,
  
  // 支持的语言
  SUPPORTED_LANGUAGES: {
    'zh-CN': '中文（简体）',
    'zh-TW': '中文（繁体）',
    'en-US': 'English (US)',
    'en-GB': 'English (UK)',
    'ja-JP': '日本語',
    'ko-KR': '한국어',
  },
} as const;

export type SpeechRecognitionState = typeof SPEECH_RECOGNITION_CONFIG.STATES[keyof typeof SPEECH_RECOGNITION_CONFIG.STATES];
export type SpeechRecognitionLanguage = keyof typeof SPEECH_RECOGNITION_CONFIG.SUPPORTED_LANGUAGES;