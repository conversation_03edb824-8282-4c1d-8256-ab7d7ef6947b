/* 练习目标抽屉样式 */
.target-drawer .drawer-content {
  padding: 0;
}

.target-drawer .framework-section {
  background: rgba(33, 128, 141, 0.1);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.target-drawer .framework-header {
  margin-bottom: 16px;
}

.target-drawer .framework-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.target-drawer .framework-logo {
  width: 24px;
  height: 24px;
  object-fit: cover;
}

.target-drawer .modules-container {
  margin-top: 16px;
}

.target-drawer .modules-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.target-drawer .module-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
}

.target-drawer .module-item:hover {
  border-color: #21808d;
  box-shadow: 0 2px 8px rgba(33, 128, 141, 0.1);
}

.target-drawer .module-name {
  font-weight: 500;
  color: #333;
  text-align: center;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .target-drawer .modules-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .target-drawer .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}